@tailwind base;
@tailwind components;
@tailwind utilities;

* {
	touch-action: manipulation;
}

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 222.2 84% 4.9%;
		--card: 0 0% 100%;
		--card-foreground: 222.2 84% 4.9%;
		--popover: 0 0% 100%;
		--popover-foreground: 222.2 84% 4.9%;
		--primary: 222.2 47.4% 11.2%;
		--primary-foreground: 210 40% 98%;
		--secondary: 210 40% 96%;
		--secondary-foreground: 222.2 84% 4.9%;
		--muted: 210 40% 96%;
		--muted-foreground: 215.4 16.3% 46.9%;
		--accent: 210 40% 96%;
		--accent-foreground: 222.2 84% 4.9%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 210 40% 98%;
		--border: 214.3 31.8% 91.4%;
		--input: 214.3 31.8% 91.4%;
		--ring: 222.2 84% 4.9%;
		--radius: 0.5rem;
	}

	.dark {
		--background: 222.2 84% 4.9%;
		--foreground: 210 40% 98%;
		--card: 222.2 84% 4.9%;
		--card-foreground: 210 40% 98%;
		--popover: 222.2 84% 4.9%;
		--popover-foreground: 210 40% 98%;
		--primary: 210 40% 98%;
		--primary-foreground: 222.2 84% 4.9%;
		--secondary: 217.2 32.6% 17.5%;
		--secondary-foreground: 210 40% 98%;
		--muted: 217.2 32.6% 17.5%;
		--muted-foreground: 215 20.2% 65.1%;
		--accent: 217.2 32.6% 17.5%;
		--accent-foreground: 210 40% 98%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 210 40% 98%;
		--border: 217.2 32.6% 17.5%;
		--input: 217.2 32.6% 17.5%;
		--ring: 212.7 26.8% 83.9%;
	}

	/* Mobile-optimized base styles */
	* {
		-webkit-tap-highlight-color: transparent;
	}

	html {
		-webkit-text-size-adjust: 100%;
		-ms-text-size-adjust: 100%;
	}

	body {
		font-feature-settings:
			'rlig' 1,
			'calt' 1;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
	}

	/* Improve touch targets on mobile */
	@media (max-width: 768px) {
		button,
		[role='button'],
		input[type='submit'],
		input[type='button'],
		input[type='reset'] {
			min-height: 44px;
			min-width: 44px;
		}
	}
}

@layer utilities {
	/* Mobile-specific utilities */
	.touch-manipulation {
		touch-action: manipulation;
	}

	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	/* Safe area insets for mobile devices */
	.safe-top {
		padding-top: env(safe-area-inset-top);
	}

	.safe-bottom {
		padding-bottom: env(safe-area-inset-bottom);
	}

	.safe-left {
		padding-left: env(safe-area-inset-left);
	}

	.safe-right {
		padding-right: env(safe-area-inset-right);
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}

@layer utilities {
	.line-clamp-2 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
	}

	.line-clamp-3 {
		overflow: hidden;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 3;
	}
}

/* Focus styles for better accessibility */
@layer base {
	*:focus-visible {
		@apply outline-2 outline-offset-2 outline-ring;
	}
}

/* Smooth scrolling */
@layer base {
	html {
		scroll-behavior: smooth;
	}
}

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
	*,
	*::before,
	*::after {
		animation-duration: 0.01ms !important;
		animation-iteration-count: 1 !important;
		transition-duration: 0.01ms !important;
		scroll-behavior: auto !important;
	}
}
