'use client';

import React, { useState } from 'react';
import { Wand2, Sun, Sunset, Moon, RotateCcw, Calendar, AlertTriangle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { useCoursePlanning } from '@/contexts/CoursePlanningContext';
import { AutoMode } from '@/types/course-planning';

const AUTO_MODES: { value: AutoMode; label: string; description: string; icon: React.ReactNode }[] = [
	{
		value: 'refer-non-overlap',
		label: 'Tối ưu tổng thể',
		description: '<PERSON><PERSON><PERSON> lịch học với ít xung đột nhất',
		icon: <Calendar className="h-4 w-4" />,
	},
	{
		value: 'refer-non-overlap-morning',
		label: 'Ưu tiên buổi sáng',
		description: 'Tối ưu lịch học buổi sáng (tiết 1-6)',
		icon: <Sun className="h-4 w-4" />,
	},
	{
		value: 'refer-non-overlap-afternoon',
		label: 'Ưu tiên buổi chiều',
		description: 'Tối ưu lịch học buổi chiều (tiết 7-12)',
		icon: <Sunset className="h-4 w-4" />,
	},
	{
		value: 'refer-non-overlap-evening',
		label: 'Ưu tiên buổi tối',
		description: 'Tối ưu lịch học buổi tối (tiết 13-16)',
		icon: <Moon className="h-4 w-4" />,
	},
];

export function ScheduleControls() {
	const { state, generateSchedule, getCalendarTableData } = useCoursePlanning();
	const [selectedMode, setSelectedMode] = useState<AutoMode>('refer-non-overlap');

	const getSelectedSubjectsCount = () => {
		return Object.values(state.selectedClasses).reduce(
			(total, majorData) =>
				total + Object.values(majorData).filter((subject) => subject.show).length,
			0
		);
	};

	const getSelectedClassesCount = () => {
		return Object.values(state.selectedClasses).reduce(
			(total, majorData) =>
				total + Object.values(majorData).filter((subject) => subject.show && subject.class).length,
			0
		);
	};

	const handleGenerateSchedule = async () => {
		await generateSchedule(selectedMode);
	};

	const handleNextSolution = async () => {
		await generateSchedule(selectedMode);
	};

	const selectedSubjectsCount = getSelectedSubjectsCount();
	const selectedClassesCount = getSelectedClassesCount();
	const calendarData = getCalendarTableData();
	const hasConflicts = calendarData && calendarData.totalConflictedSessions > 0;

	const canGenerate = state.calendar && selectedSubjectsCount > 0;
	const hasIncompleteSelection = selectedSubjectsCount > selectedClassesCount;

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Wand2 className="h-5 w-5" />
					Tạo lịch học tự động
				</CardTitle>
				<CardDescription>
					Chọn chế độ tối ưu và tạo lịch học tự động từ các môn đã chọn
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-6">
				{/* Selection Summary */}
				<div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg">
					<div className="flex items-center gap-2">
						<Badge variant="outline">
							{selectedSubjectsCount} môn đã chọn
						</Badge>
						<Badge variant={selectedClassesCount === selectedSubjectsCount ? "default" : "secondary"}>
							{selectedClassesCount} lớp đã chọn
						</Badge>
					</div>
					{hasConflicts && (
						<Badge variant="destructive" className="flex items-center gap-1">
							<AlertTriangle className="h-3 w-3" />
							{calendarData.totalConflictedSessions} tiết trung
						</Badge>
					)}
				</div>

				{/* Mode Selection */}
				<div className="space-y-3">
					<label className="text-sm font-medium">Chế độ tối ưu hóa</label>
					<Select value={selectedMode} onValueChange={(value: AutoMode) => setSelectedMode(value)}>
						<SelectTrigger>
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							{AUTO_MODES.map((mode) => (
								<SelectItem key={mode.value} value={mode.value}>
									<div className="flex items-center gap-2">
										{mode.icon}
										<div>
											<div className="font-medium">{mode.label}</div>
											<div className="text-xs text-muted-foreground">{mode.description}</div>
										</div>
									</div>
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				{/* Warnings */}
				{hasIncompleteSelection && (
					<Alert>
						<AlertTriangle className="h-4 w-4" />
						<AlertDescription>
							Một số môn học chưa được chọn lớp. Hệ thống sẽ tự động chọn lớp tối ưu cho các môn này.
						</AlertDescription>
					</Alert>
				)}

				{!canGenerate && (
					<Alert variant="destructive">
						<AlertTriangle className="h-4 w-4" />
						<AlertDescription>
							{!state.calendar
								? 'Vui lòng tải lên file Excel trước khi tạo lịch'
								: 'Vui lòng chọn ít nhất một môn học'}
						</AlertDescription>
					</Alert>
				)}

				{/* Action Buttons */}
				<div className="flex gap-3">
					<Button
						onClick={handleGenerateSchedule}
						disabled={!canGenerate || state.loading}
						className="flex-1"
					>
						{state.loading ? (
							<div className="flex items-center gap-2">
								<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
								Đang tạo lịch...
							</div>
						) : (
							<div className="flex items-center gap-2">
								<Wand2 className="h-4 w-4" />
								Tạo lịch tự động
							</div>
						)}
					</Button>

					{state.autoTh >= 0 && (
						<Button
							variant="outline"
							onClick={handleNextSolution}
							disabled={!canGenerate || state.loading}
						>
							<RotateCcw className="h-4 w-4" />
							Giải pháp khác
						</Button>
					)}
				</div>

				{/* Results Info */}
				{state.autoTh >= 0 && !state.loading && (
					<div className="text-sm text-muted-foreground space-y-1">
						<p>Giải pháp thứ {state.autoTh + 1}</p>
						{calendarData && (
							<p>
								{calendarData.totalConflictedSessions === 0
									? '✅ Không có xung đột thời gian'
									: `⚠️ ${calendarData.totalConflictedSessions} tiết học bị trùng`}
							</p>
						)}
					</div>
				)}

				{/* Instructions */}
				<div className="text-xs text-muted-foreground space-y-1">
					<p><strong>Hướng dẫn:</strong></p>
					<ul className="list-disc list-inside space-y-1 ml-2">
						<li>Chọn các môn học bạn muốn đăng ký</li>
						<li>Chọn chế độ tối ưu phù hợp với thời gian học mong muốn</li>
						<li>Nhấn "Tạo lịch tự động" để hệ thống tìm lịch học tối ưu</li>
						<li>Sử dụng "Giải pháp khác" để xem các phương án khác</li>
					</ul>
				</div>
			</CardContent>
		</Card>
	);
}
