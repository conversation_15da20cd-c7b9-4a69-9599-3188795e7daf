'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronRight, BookOpen, Users, Clock, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useCoursePlanning } from '@/contexts/CoursePlanningContext';
import { Field } from '@/types/course-planning';

export function SubjectSelection() {
	const { state, selectMajor, updateSelectedClass, updateShowSubject } = useCoursePlanning();
	const [expandedMajors, setExpandedMajors] = useState<Set<string>>(new Set());

	if (!state.calendar) {
		return (
			<Card>
				<CardContent className="p-6">
					<p className="text-center text-muted-foreground">
						Vui lòng tải lên file Excel để bắt đầu chọn môn học
					</p>
				</CardContent>
			</Card>
		);
	}

	const toggleMajor = (majorKey: string) => {
		const newExpanded = new Set(expandedMajors);
		if (newExpanded.has(majorKey)) {
			newExpanded.delete(majorKey);
		} else {
			newExpanded.add(majorKey);
		}
		setExpandedMajors(newExpanded);
	};

	const handleMajorSelect = (majorKey: string, checked: boolean) => {
		selectMajor(majorKey, checked);
	};

	const handleSubjectShow = (majorKey: string, subjectName: string, checked: boolean) => {
		updateShowSubject(majorKey, subjectName, checked);
	};

	const handleClassSelect = (majorKey: string, subjectName: string, classCode: string) => {
		updateSelectedClass(majorKey, subjectName, classCode);
	};

	const getMajorStats = (majorKey: string) => {
		const majorData = state.calendar!.majors[majorKey];
		const totalSubjects = Object.keys(majorData).length;
		const selectedSubjects = Object.keys(state.selectedClasses[majorKey] || {}).filter(
			(subjectName) => state.selectedClasses[majorKey]?.[subjectName]?.show
		).length;
		
		return { totalSubjects, selectedSubjects };
	};

	const getSubjectStats = (majorKey: string, subjectName: string) => {
		const subjectData = state.calendar!.majors[majorKey][subjectName];
		const totalClasses = Object.keys(subjectData).length;
		const selectedClass = state.selectedClasses[majorKey]?.[subjectName]?.class;
		
		return { totalClasses, selectedClass };
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<BookOpen className="h-5 w-5" />
					Chọn môn học
				</CardTitle>
				<CardDescription>
					Chọn các môn học bạn muốn đăng ký và lớp học tương ứng
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4">
				{Object.entries(state.calendar.majors).map(([majorKey, majorData]) => {
					const { totalSubjects, selectedSubjects } = getMajorStats(majorKey);
					const isExpanded = expandedMajors.has(majorKey);
					const isMajorSelected = selectedSubjects > 0;

					return (
						<Collapsible key={majorKey} open={isExpanded} onOpenChange={() => toggleMajor(majorKey)}>
							<div className="border rounded-lg">
								<CollapsibleTrigger asChild>
									<div className="flex items-center justify-between p-4 hover:bg-muted/50 cursor-pointer">
										<div className="flex items-center gap-3">
											{isExpanded ? (
												<ChevronDown className="h-4 w-4" />
											) : (
												<ChevronRight className="h-4 w-4" />
											)}
											<div className="flex items-center gap-2">
												<Checkbox
													checked={isMajorSelected}
													onCheckedChange={(checked) => 
														handleMajorSelect(majorKey, checked as boolean)
													}
													onClick={(e) => e.stopPropagation()}
												/>
												<span className="font-medium">{majorKey}</span>
											</div>
										</div>
										<div className="flex items-center gap-2">
											<Badge variant={isMajorSelected ? "default" : "secondary"}>
												{selectedSubjects}/{totalSubjects} môn
											</Badge>
										</div>
									</div>
								</CollapsibleTrigger>

								<CollapsibleContent>
									<div className="border-t">
										{Object.entries(majorData).map(([subjectName, subjectData]) => {
											const { totalClasses, selectedClass } = getSubjectStats(majorKey, subjectName);
											const isSubjectSelected = state.selectedClasses[majorKey]?.[subjectName]?.show || false;
											const availableClasses = Object.keys(subjectData);

											return (
												<div key={subjectName} className="p-4 border-b last:border-b-0">
													<div className="flex items-start justify-between gap-4">
														<div className="flex items-start gap-3 flex-1">
															<Checkbox
																checked={isSubjectSelected}
																onCheckedChange={(checked) =>
																	handleSubjectShow(majorKey, subjectName, checked as boolean)
																}
															/>
															<div className="flex-1 space-y-2">
																<div>
																	<h4 className="font-medium">{subjectName}</h4>
																	<div className="flex items-center gap-4 text-sm text-muted-foreground">
																		<span className="flex items-center gap-1">
																			<Users className="h-3 w-3" />
																			{totalClasses} lớp
																		</span>
																	</div>
																</div>

																{isSubjectSelected && (
																	<div className="space-y-2">
																		<Select
																			value={selectedClass || ''}
																			onValueChange={(value) =>
																				handleClassSelect(majorKey, subjectName, value)
																			}
																		>
																			<SelectTrigger className="w-full">
																				<SelectValue placeholder="Chọn lớp học" />
																			</SelectTrigger>
																			<SelectContent>
																				{availableClasses.map((classCode) => {
																					const classData = subjectData[classCode];
																					const scheduleCount = classData.schedules.length;
																					
																					return (
																						<SelectItem key={classCode} value={classCode}>
																							<div className="flex items-center justify-between w-full">
																								<span>{classCode}</span>
																								<div className="flex items-center gap-2 text-xs text-muted-foreground ml-2">
																									<Clock className="h-3 w-3" />
																									{scheduleCount} lịch
																								</div>
																							</div>
																						</SelectItem>
																					);
																				})}
																			</SelectContent>
																		</Select>

																		{selectedClass && (
																			<div className="text-xs text-muted-foreground space-y-1">
																				<div className="flex items-center gap-1">
																					<Users className="h-3 w-3" />
																					GV: {subjectData[selectedClass][Field.Teacher]}
																				</div>
																				<div className="flex items-center gap-1">
																					<Clock className="h-3 w-3" />
																					{subjectData[selectedClass].schedules.length} buổi học
																				</div>
																			</div>
																		)}
																	</div>
																)}
															</div>
														</div>
													</div>
												</div>
											);
										})}
									</div>
								</CollapsibleContent>
							</div>
						</Collapsible>
					);
				})}

				{Object.keys(state.calendar.majors).length === 0 && (
					<div className="text-center py-8 text-muted-foreground">
						<BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
						<p>Không tìm thấy dữ liệu môn học trong file</p>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
